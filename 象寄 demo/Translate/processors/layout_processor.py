"""
布局分析处理器
负责文本布局模式识别和对齐策略分析
"""
import cv2
import numpy as np
from typing import List, Dict, Any, Tuple
from collections import defaultdict
import math

from models.data_models import TextRegion, LayoutResult, ProcessingResult
from config.settings import get_config_manager


class LayoutProcessor:
    """布局分析处理器"""
    
    def __init__(self):
        """初始化布局分析处理器"""
        self.config_manager = get_config_manager()
        self.alignment_threshold = self.config_manager.config.alignment_threshold
        self.proximity_threshold = self.config_manager.config.proximity_threshold
        
        print(f"布局分析处理器初始化完成")
        print(f"  对齐阈值: {self.alignment_threshold}px")
        print(f"  邻近阈值: {self.proximity_threshold}px")
    
    def analyze_layout(self, chinese_regions: List[TextRegion]) -> ProcessingResult:
        """
        分析文本布局
        
        Args:
            chinese_regions: 中文文字区域列表
            
        Returns:
            ProcessingResult: 包含LayoutResult的处理结果
        """
        try:
            print(f"\n=== 开始布局分析 ===")
            print(f"分析 {len(chinese_regions)} 个中文文字区域")
            
            if len(chinese_regions) < 2:
                print("文字区域少于2个，使用简单布局")
                layout_result = self._create_simple_layout_result(chinese_regions)
            else:
                # 转换为内部数据格式
                regions_data = self._convert_regions_to_dict(chinese_regions)
                
                # 1. 水平对齐检测
                horizontal_alignment = self._detect_horizontal_alignment(regions_data)
                
                # 2. 垂直分布分析
                vertical_distribution = self._analyze_vertical_distribution(regions_data)
                
                # 3. 间距模式分析
                spacing_pattern = self._analyze_spacing_pattern(regions_data)
                
                # 4. 布局模式识别
                layout_mode = self._identify_layout_mode(
                    regions_data, horizontal_alignment, vertical_distribution, spacing_pattern
                )
                
                # 5. 生成对齐策略
                alignment_strategies = self._generate_alignment_strategies(
                    layout_mode, horizontal_alignment, vertical_distribution
                )
                
                # 创建布局结果
                layout_result = LayoutResult(
                    layout_mode=layout_mode,
                    regions=regions_data,
                    horizontal_alignment=horizontal_alignment,
                    vertical_distribution=vertical_distribution,
                    alignment_strategies=alignment_strategies
                )
            
            self._print_layout_analysis_result(layout_result)
            print("=== 布局分析完成 ===\n")
            
            return ProcessingResult.success_result(layout_result)
            
        except Exception as e:
            error_msg = f"布局分析失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _convert_regions_to_dict(self, regions: List[TextRegion]) -> List[Dict[str, Any]]:
        """将TextRegion转换为字典格式"""
        regions_data = []
        for region in regions:
            x, y, w, h = region.bbox
            regions_data.append({
                'id': region.id,
                'text': region.text,
                'bbox': region.bbox,
                'center': region.center,
                'left': x,
                'right': x + w,
                'top': y,
                'bottom': y + h
            })
        return regions_data
    
    def _detect_horizontal_alignment(self, regions: List[Dict]) -> Dict[str, Any]:
        """检测水平对齐模式"""
        if len(regions) < 2:
            return {'type': 'single', 'left_groups': [], 'center_groups': [], 'right_groups': []}
        
        # 检测左对齐组
        left_groups = self._find_aligned_groups(regions, 'left')
        
        # 检测右对齐组
        right_groups = self._find_aligned_groups(regions, 'right')
        
        # 检测居中对齐组
        center_groups = self._find_aligned_groups(regions, 'center')
        
        # 确定主要对齐模式
        alignment_type = self._determine_primary_alignment(left_groups, center_groups, right_groups)
        
        return {
            'type': alignment_type,
            'left_groups': left_groups,
            'center_groups': center_groups,
            'right_groups': right_groups
        }
    
    def _find_aligned_groups(self, regions: List[Dict], align_type: str) -> List[List[Dict]]:
        """查找对齐组"""
        if align_type == 'left':
            key_func = lambda r: r['left']
        elif align_type == 'right':
            key_func = lambda r: r['right']
        else:  # center
            key_func = lambda r: r['center'][0]
        
        # 按对齐位置分组
        position_groups = defaultdict(list)
        for region in regions:
            pos = key_func(region)
            position_groups[pos].append(region)
        
        # 合并相近位置的组
        aligned_groups = []
        sorted_positions = sorted(position_groups.keys())
        
        current_group = []
        for pos in sorted_positions:
            if not current_group:
                current_group.extend(position_groups[pos])
            else:
                # 检查是否与当前组的位置接近
                last_pos = key_func(current_group[-1])
                if abs(pos - last_pos) <= self.alignment_threshold:
                    current_group.extend(position_groups[pos])
                else:
                    if len(current_group) >= 2:
                        aligned_groups.append(current_group)
                    current_group = list(position_groups[pos])
        
        # 添加最后一组
        if len(current_group) >= 2:
            aligned_groups.append(current_group)
        
        return aligned_groups
    
    def _determine_primary_alignment(self, left_groups, center_groups, right_groups) -> str:
        """确定主要对齐模式"""
        left_count = sum(len(group) for group in left_groups)
        center_count = sum(len(group) for group in center_groups)
        right_count = sum(len(group) for group in right_groups)
        
        if left_count > center_count and left_count > right_count:
            return 'left'
        elif right_count > center_count and right_count > left_count:
            return 'right'
        elif center_count > 0:
            return 'center'
        else:
            return 'mixed'
    
    def _analyze_vertical_distribution(self, regions: List[Dict]) -> Dict[str, Any]:
        """分析垂直分布模式"""
        if len(regions) < 2:
            return {'type': 'single', 'rows': 1, 'columns': 1}
        
        # 按Y坐标分组找行
        y_positions = [r['top'] for r in regions]
        y_groups = self._group_by_proximity(y_positions, self.proximity_threshold)
        rows = len(y_groups)
        
        # 按X坐标分组找列
        x_positions = [r['left'] for r in regions]
        x_groups = self._group_by_proximity(x_positions, self.proximity_threshold)
        columns = len(x_groups)
        
        # 确定分布类型
        if rows == 1 and columns > 1:
            dist_type = 'horizontal'
        elif rows > 1 and columns == 1:
            dist_type = 'vertical'
        elif rows == 2 and columns == 2:
            dist_type = 'grid_2x2'
        elif rows > 2 or columns > 2:
            dist_type = 'complex'
        else:
            dist_type = 'scattered'
        
        return {
            'type': dist_type,
            'rows': rows,
            'columns': columns,
            'row_groups': y_groups,
            'column_groups': x_groups
        }
    
    def _group_by_proximity(self, positions: List[int], threshold: int) -> List[List[int]]:
        """按邻近度分组"""
        if not positions:
            return []
        
        sorted_positions = sorted(set(positions))
        groups = []
        current_group = [sorted_positions[0]]
        
        for i in range(1, len(sorted_positions)):
            if sorted_positions[i] - sorted_positions[i-1] <= threshold:
                current_group.append(sorted_positions[i])
            else:
                groups.append(current_group)
                current_group = [sorted_positions[i]]
        
        groups.append(current_group)
        return groups
    
    def _analyze_spacing_pattern(self, regions: List[Dict]) -> Dict[str, Any]:
        """分析间距模式"""
        if len(regions) < 2:
            return {'type': 'none'}
        
        # 计算水平间距
        h_spacings = []
        for i in range(len(regions)):
            for j in range(i + 1, len(regions)):
                r1, r2 = regions[i], regions[j]
                # 只计算同一行的间距
                if abs(r1['top'] - r2['top']) <= self.alignment_threshold:
                    spacing = abs(r1['center'][0] - r2['center'][0])
                    h_spacings.append(spacing)
        
        # 计算垂直间距
        v_spacings = []
        for i in range(len(regions)):
            for j in range(i + 1, len(regions)):
                r1, r2 = regions[i], regions[j]
                # 只计算同一列的间距
                if abs(r1['center'][0] - r2['center'][0]) <= self.alignment_threshold:
                    spacing = abs(r1['center'][1] - r2['center'][1])
                    v_spacings.append(spacing)
        
        # 分析间距规律性
        h_regularity = self._analyze_spacing_regularity(h_spacings)
        v_regularity = self._analyze_spacing_regularity(v_spacings)
        
        return {
            'type': 'regular' if h_regularity == 'regular' and v_regularity == 'regular' else 'irregular',
            'horizontal_regularity': h_regularity,
            'vertical_regularity': v_regularity,
            'horizontal_spacings': h_spacings,
            'vertical_spacings': v_spacings
        }
    
    def _analyze_spacing_regularity(self, spacings: List[int]) -> str:
        """分析间距规律性"""
        if len(spacings) < 2:
            return 'insufficient_data'
        
        # 计算变异系数
        mean_spacing = np.mean(spacings)
        std_spacing = np.std(spacings)
        
        if mean_spacing == 0:
            return 'zero_spacing'
        
        cv = std_spacing / mean_spacing
        
        if cv < 0.2:
            return 'regular'
        elif cv < 0.5:
            return 'semi_regular'
        else:
            return 'irregular'

    def _identify_layout_mode(
        self,
        regions: List[Dict],
        h_align: Dict,
        v_dist: Dict,
        spacing: Dict
    ) -> str:
        """识别整体布局模式"""
        num_regions = len(regions)

        if num_regions == 1:
            return 'single_text'
        elif num_regions == 2:
            return 'dual_text'

        # 基于垂直分布判断
        if v_dist['type'] == 'horizontal':
            if h_align['type'] == 'left':
                return 'horizontal_left_aligned'
            elif h_align['type'] == 'center':
                return 'horizontal_center_aligned'
            elif h_align['type'] == 'right':
                return 'horizontal_right_aligned'
            else:
                return 'horizontal_mixed'
        elif v_dist['type'] == 'vertical':
            if h_align['type'] == 'left':
                return 'vertical_left_aligned'
            elif h_align['type'] == 'center':
                return 'vertical_center_aligned'
            else:
                return 'vertical_mixed'
        elif v_dist['type'] == 'grid_2x2':
            return 'grid_2x2'
        elif v_dist['type'] == 'complex':
            return 'complex_layout'
        else:
            return 'scattered_layout'

    def _generate_alignment_strategies(
        self,
        layout_mode: str,
        h_align: Dict,
        v_dist: Dict
    ) -> List[Dict[str, Any]]:
        """生成对齐策略建议"""
        strategies = []

        if layout_mode == 'horizontal_left_aligned':
            strategies.append({
                'type': 'preserve_left_alignment',
                'description': '保持左对齐布局',
                'regions': 'all',
                'priority': 'high'
            })
        elif layout_mode == 'horizontal_center_aligned':
            strategies.append({
                'type': 'preserve_center_alignment',
                'description': '保持居中对齐布局',
                'regions': 'all',
                'priority': 'high'
            })
        elif layout_mode == 'vertical_left_aligned':
            strategies.append({
                'type': 'preserve_vertical_left_alignment',
                'description': '保持垂直左对齐布局',
                'regions': 'all',
                'priority': 'high'
            })
        elif layout_mode == 'grid_2x2':
            strategies.append({
                'type': 'preserve_grid_layout',
                'description': '保持2x2网格布局',
                'regions': 'all',
                'priority': 'high'
            })
        elif layout_mode == 'complex_layout':
            # 复杂布局，提供多种策略
            if len(h_align['left_groups']) > 0:
                strategies.append({
                    'type': 'group_left_alignment',
                    'description': '对左对齐组保持左对齐',
                    'regions': 'left_aligned_groups',
                    'priority': 'medium'
                })
            if len(h_align['center_groups']) > 0:
                strategies.append({
                    'type': 'group_center_alignment',
                    'description': '对居中组保持居中对齐',
                    'regions': 'center_aligned_groups',
                    'priority': 'medium'
                })
            if len(h_align['right_groups']) > 0:
                strategies.append({
                    'type': 'group_right_alignment',
                    'description': '对右对齐组保持右对齐',
                    'regions': 'right_aligned_groups',
                    'priority': 'medium'
                })
        else:
            # 默认策略
            strategies.append({
                'type': 'center_alignment',
                'description': '使用居中对齐（默认策略）',
                'regions': 'all',
                'priority': 'low'
            })

        return strategies

    def _create_simple_layout_result(self, regions: List[TextRegion]) -> LayoutResult:
        """创建简单布局结果"""
        regions_data = self._convert_regions_to_dict(regions)

        return LayoutResult(
            layout_mode='single_text' if len(regions) <= 1 else 'simple',
            regions=regions_data,
            horizontal_alignment={'type': 'single', 'left_groups': [], 'center_groups': [], 'right_groups': []},
            vertical_distribution={'type': 'single', 'rows': 1, 'columns': 1},
            alignment_strategies=[{
                'type': 'center_alignment',
                'description': '使用居中对齐',
                'regions': 'all',
                'priority': 'default'
            }]
        )

    def _print_layout_analysis_result(self, result: LayoutResult):
        """打印布局分析结果"""
        print(f"\n布局分析结果:")
        print(f"  文字区域数量: {len(result.regions)}")
        print(f"  布局模式: {result.layout_mode}")
        print(f"  水平对齐: {result.horizontal_alignment['type']}")
        print(f"  垂直分布: {result.vertical_distribution['type']} "
              f"({result.vertical_distribution['rows']}行 × {result.vertical_distribution['columns']}列)")

        # 打印对齐组信息
        h_align = result.horizontal_alignment
        if h_align['type'] != 'single':
            print(f"  对齐组信息:")
            if h_align['left_groups']:
                print(f"    左对齐组: {len(h_align['left_groups'])}个")
            if h_align['center_groups']:
                print(f"    居中对齐组: {len(h_align['center_groups'])}个")
            if h_align['right_groups']:
                print(f"    右对齐组: {len(h_align['right_groups'])}个")

        # 打印策略建议
        if result.alignment_strategies:
            print(f"  推荐策略:")
            for strategy in result.alignment_strategies:
                print(f"    - {strategy['description']} (优先级: {strategy['priority']})")

    def get_alignment_for_region(self, region_bbox: Tuple[int, int, int, int], layout_result: LayoutResult) -> str:
        """
        为特定区域获取对齐方式

        Args:
            region_bbox: 区域边界框 (x, y, w, h)
            layout_result: 布局分析结果

        Returns:
            str: 对齐方式 ('left', 'center', 'right')
        """
        x, y, w, h = region_bbox
        center_x = x + w // 2

        try:
            h_align = layout_result.horizontal_alignment

            # 检查左对齐组
            for group in h_align.get('left_groups', []):
                for region in group:
                    if (abs(region.get('left', 0) - x) <= self.alignment_threshold and
                        abs(region.get('top', 0) - y) <= self.alignment_threshold):
                        return 'left'

            # 检查右对齐组
            for group in h_align.get('right_groups', []):
                for region in group:
                    if (abs(region.get('right', 0) - (x + w)) <= self.alignment_threshold and
                        abs(region.get('top', 0) - y) <= self.alignment_threshold):
                        return 'right'

            # 检查居中对齐组
            for group in h_align.get('center_groups', []):
                for region in group:
                    region_center_x = region.get('center', [0, 0])[0]
                    if abs(region_center_x - center_x) <= self.alignment_threshold:
                        return 'center'

            # 默认居中对齐
            return 'center'

        except Exception as e:
            print(f"获取区域对齐方式失败: {e}")
            return 'center'
