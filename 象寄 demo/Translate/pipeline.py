"""
翻译流水线主控制器
协调各个处理模块完成图像翻译任务
"""
import os
import cv2
from typing import Optional, Dict, Any

from models.data_models import ProcessingResult, PipelineConfig
from config.settings import get_config_manager
from processors.ocr_processor import OCRProcessor
from processors.font_processor import FontProcessor
from processors.translation_processor import TranslationProcessor
from processors.inpaint_processor import InpaintProcessor
from processors.renderer import Renderer
from core.layout_analyzer import LayoutAnalyzer
from utils.visualizer import Visualizer


class TranslationPipeline:
    """翻译流水线"""
    
    def __init__(self, font_weight: int = 400, enable_debug: bool = True):
        """
        初始化翻译流水线
        
        Args:
            font_weight: 字体粗细 (100-900)
            enable_debug: 是否启用调试输出
        """
        self.config_manager = get_config_manager()
        self.config = self.config_manager.config
        self.enable_debug = enable_debug
        
        # 更新配置
        self.config_manager.update_config(
            default_font_weight=font_weight,
            enable_debug_output=enable_debug
        )
        
        # 初始化处理器
        self.ocr_processor = OCRProcessor()
        self.font_processor = FontProcessor()
        self.translation_processor = TranslationProcessor(font_weight)
        self.inpaint_processor = InpaintProcessor()
        self.renderer = Renderer(font_weight)
        self.layout_analyzer = LayoutAnalyzer()
        
        # 初始化可视化工具
        if enable_debug:
            self.visualizer = Visualizer()
        else:
            self.visualizer = None
        
        print(f"翻译流水线初始化完成")
        print(f"  字体粗细: {font_weight}")
        print(f"  调试模式: {'开启' if enable_debug else '关闭'}")
    
    def process_image(self, image_path: str) -> ProcessingResult:
        """
        处理图像进行翻译
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            ProcessingResult: 处理结果
        """
        try:
            # 检查输入文件
            if not os.path.exists(image_path):
                return ProcessingResult.error_result(f"图像文件不存在: {image_path}")
            
            print(f"\n{'='*60}")
            print(f"开始处理图像: {image_path}")
            print(f"{'='*60}")
            
            # 确保输出目录存在
            self.config_manager.ensure_output_dir()
            
            # 第一阶段：OCR文字检测与识别
            print(f"\n第一阶段：OCR文字检测与识别")
            print("-" * 40)
            ocr_result = self.ocr_processor.process_image(
                image_path, self.config.ocr_confidence_threshold
            )
            if not ocr_result.success:
                return ocr_result
            
            ocr_data = ocr_result.data
            
            # 如果没有中文文字，直接返回
            if len(ocr_data.chinese_regions) == 0:
                print("未检测到中文文字，处理结束")
                return ProcessingResult.success_result({
                    'message': '未检测到中文文字',
                    'original_image': cv2.imread(image_path)
                })
            
            # 第二阶段：布局分析
            print(f"\n第二阶段：布局分析")
            print("-" * 40)
            layout_result = self.layout_analyzer.analyze_text_layout(
                ocr_data.dt_polys, ocr_data.rec_texts, ocr_data.rec_scores
            )
            
            # 第三阶段：字体匹配
            print(f"\n第三阶段：字体匹配")
            print("-" * 40)
            image = cv2.imread(image_path)
            font_result = self.font_processor.process_regions(image, ocr_data.chinese_regions)
            if not font_result.success:
                return font_result
            
            font_data = font_result.data
            
            # 第四阶段：翻译处理
            print(f"\n第四阶段：翻译处理")
            print("-" * 40)
            translation_result = self.translation_processor.process_translation(
                image, ocr_data.chinese_regions, font_data, layout_result
            )
            if not translation_result.success:
                return translation_result
            
            translation_data = translation_result.data
            
            # 如果没有翻译结果，直接返回
            if len(translation_data) == 0:
                print("没有找到可翻译的文字，处理结束")
                return ProcessingResult.success_result({
                    'message': '没有找到可翻译的文字',
                    'original_image': image
                })
            
            # 第五阶段：图像修复（去除原文字）
            print(f"\n第五阶段：图像修复")
            print("-" * 40)
            inpaint_result = self.inpaint_processor.process_inpainting(
                image, ocr_data.chinese_regions, self.enable_debug
            )
            if not inpaint_result.success:
                return inpaint_result
            
            inpainted_image = inpaint_result.data
            
            # 第六阶段：渲染译文
            print(f"\n第六阶段：渲染译文")
            print("-" * 40)
            render_result = self.renderer.render_translations(
                inpainted_image, translation_data, layout_result
            )
            if not render_result.success:
                return render_result
            
            render_data = render_result.data
            final_image = render_data['image']
            render_log = render_data['render_log']
            
            # 保存最终结果
            final_path = self.renderer.save_final_image(final_image)
            
            # 生成调试图像
            if self.enable_debug and self.visualizer:
                self._generate_debug_images(
                    image_path, ocr_data, layout_result, translation_data
                )
            
            # 返回处理结果
            result_data = {
                'final_image': final_image,
                'final_path': final_path,
                'render_log': render_log,
                'ocr_result': ocr_data,
                'layout_result': layout_result,
                'translation_results': translation_data
            }
            
            print(f"\n{'='*60}")
            print(f"处理完成！")
            print(f"  翻译文字数量: {len(render_log)}")
            print(f"  最终图像: {final_path}")
            print(f"{'='*60}")
            
            return ProcessingResult.success_result(result_data)
            
        except Exception as e:
            error_msg = f"流水线处理失败: {str(e)}"
            print(f"\n错误: {error_msg}")
            return ProcessingResult.error_result(error_msg)
    
    def _generate_debug_images(self, image_path: str, ocr_data, layout_result, translation_data):
        """生成调试图像"""
        try:
            print(f"\n生成调试图像...")
            
            # OCR检测结果
            self.visualizer.draw_ocr_detection(image_path, ocr_data)
            
            # 布局分析结果
            self.visualizer.draw_layout_analysis(image_path, layout_result)
            
            # 翻译结果
            self.visualizer.draw_translation_result(image_path, translation_data)
            
            print("调试图像生成完成")
            
        except Exception as e:
            print(f"生成调试图像失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            self.ocr_processor.cleanup()
            print("流水线资源清理完成")
        except Exception as e:
            print(f"资源清理失败: {e}")
    
    def get_config(self) -> PipelineConfig:
        """获取当前配置"""
        return self.config
    
    def update_config(self, **kwargs):
        """更新配置"""
        self.config_manager.update_config(**kwargs)
        self.config = self.config_manager.config
    
    def get_translation_stats(self) -> Dict[str, Any]:
        """获取翻译统计信息"""
        return {
            'translation_dict_size': len(self.config_manager.translation_dict),
            'font_mapping_size': len(self.config_manager.font_mapping),
            'fonts_dir': self.config_manager.get_fonts_dir(),
            'output_dir': self.config_manager.get_output_dir()
        }
