import numpy as np
import cv2
from collections import defaultdict
import math

class LayoutAnalyzer:
    """文本布局分析器 - 第一阶段：布局模式识别"""
    
    def __init__(self):
        self.alignment_threshold = 5  # 对齐判断的像素阈值
        self.proximity_threshold = 50  # 文字间距判断阈值
        
    def analyze_text_layout(self, dt_polys, rec_texts, rec_scores):
        """分析文本布局模式"""
        print("\n=== 开始布局分析 ===")
        
        # 只处理中文文字
        chinese_regions = []
        for i, (poly, text, score) in enumerate(zip(dt_polys, rec_texts, rec_scores)):
            if self.is_chinese_text(text):
                points = np.array(poly, dtype=np.int32)
                x, y, w, h = cv2.boundingRect(points)
                chinese_regions.append({
                    'id': i,
                    'text': text,
                    'bbox': (x, y, w, h),
                    'center': (x + w//2, y + h//2),
                    'left': x,
                    'right': x + w,
                    'top': y,
                    'bottom': y + h
                })
        
        if len(chinese_regions) < 2:
            print("文字区域少于2个，跳过布局分析")
            return self._create_simple_layout_result(chinese_regions)
        
        # 1. 水平对齐检测
        horizontal_alignment = self._detect_horizontal_alignment(chinese_regions)
        
        # 2. 垂直分布分析
        vertical_distribution = self._analyze_vertical_distribution(chinese_regions)
        
        # 3. 间距模式分析
        spacing_pattern = self._analyze_spacing_pattern(chinese_regions)
        
        # 4. 布局模式识别
        layout_mode = self._identify_layout_mode(
            chinese_regions, horizontal_alignment, vertical_distribution, spacing_pattern
        )
        
        result = {
            'regions': chinese_regions,
            'horizontal_alignment': horizontal_alignment,
            'vertical_distribution': vertical_distribution,
            'spacing_pattern': spacing_pattern,
            'layout_mode': layout_mode
        }
        
        self._print_layout_analysis_result(result)
        return result
    
    def _detect_horizontal_alignment(self, regions):
        """检测水平对齐模式"""
        if len(regions) < 2:
            return {'type': 'single', 'groups': []}
        
        # 按垂直位置排序
        regions_by_y = sorted(regions, key=lambda r: r['top'])
        
        # 检测左对齐
        left_aligned_groups = self._find_aligned_groups(regions, 'left')
        
        # 检测右对齐
        right_aligned_groups = self._find_aligned_groups(regions, 'right')
        
        # 检测居中对齐
        center_aligned_groups = self._find_aligned_groups(regions, 'center_x')
        
        # 确定主要对齐模式
        alignment_type = 'mixed'
        dominant_groups = []
        
        if len(left_aligned_groups) > len(right_aligned_groups) and len(left_aligned_groups) > len(center_aligned_groups):
            alignment_type = 'left'
            dominant_groups = left_aligned_groups
        elif len(right_aligned_groups) > len(center_aligned_groups):
            alignment_type = 'right'
            dominant_groups = right_aligned_groups
        elif len(center_aligned_groups) >= 2:
            alignment_type = 'center'
            dominant_groups = center_aligned_groups
        
        return {
            'type': alignment_type,
            'left_groups': left_aligned_groups,
            'right_groups': right_aligned_groups,
            'center_groups': center_aligned_groups,
            'dominant_groups': dominant_groups
        }
    
    def _find_aligned_groups(self, regions, align_type):
        """找到指定对齐方式的文字组"""
        if align_type == 'left':
            positions = [r['left'] for r in regions]
        elif align_type == 'right':
            positions = [r['right'] for r in regions]
        elif align_type == 'center_x':
            positions = [r['center'][0] for r in regions]
        else:
            return []
        
        # 按位置分组
        groups = []
        used_indices = set()
        
        for i, pos1 in enumerate(positions):
            if i in used_indices:
                continue
                
            group = [i]
            used_indices.add(i)
            
            for j, pos2 in enumerate(positions):
                if j in used_indices or i == j:
                    continue
                
                if abs(pos1 - pos2) <= self.alignment_threshold:
                    group.append(j)
                    used_indices.add(j)
            
            if len(group) >= 2:  # 至少2个文字才算一组
                groups.append([regions[idx] for idx in group])
        
        return groups
    
    def _analyze_vertical_distribution(self, regions):
        """分析垂直分布模式"""
        if len(regions) < 2:
            return {'type': 'single', 'columns': 1, 'rows': 1}
        
        # 按Y坐标排序找行
        y_positions = [r['center'][1] for r in regions]
        y_groups = self._cluster_positions(y_positions, threshold=20)
        
        # 按X坐标排序找列
        x_positions = [r['center'][0] for r in regions]
        x_groups = self._cluster_positions(x_positions, threshold=self.proximity_threshold)
        
        rows = len(y_groups)
        columns = len(x_groups)
        
        # 判断分布类型
        if rows == 1 and columns == 1:
            dist_type = 'single'
        elif rows == 1:
            dist_type = 'horizontal'
        elif columns == 1:
            dist_type = 'vertical'
        elif rows == 2 and columns == 2:
            dist_type = 'grid_2x2'
        else:
            dist_type = 'complex'
        
        return {
            'type': dist_type,
            'rows': rows,
            'columns': columns,
            'y_groups': y_groups,
            'x_groups': x_groups
        }
    
    def _cluster_positions(self, positions, threshold):
        """将位置聚类成组"""
        if not positions:
            return []
        
        sorted_positions = sorted(positions)
        groups = []
        current_group = [sorted_positions[0]]
        
        for pos in sorted_positions[1:]:
            if pos - current_group[-1] <= threshold:
                current_group.append(pos)
            else:
                groups.append(current_group)
                current_group = [pos]
        
        groups.append(current_group)
        return groups
    
    def _analyze_spacing_pattern(self, regions):
        """分析间距模式"""
        if len(regions) < 2:
            return {'type': 'none', 'pattern': 'single'}
        
        # 计算水平间距
        horizontal_spacings = []
        for i, r1 in enumerate(regions):
            for j, r2 in enumerate(regions):
                if i >= j:
                    continue
                
                # 如果在同一水平线上
                if abs(r1['center'][1] - r2['center'][1]) <= 20:
                    spacing = abs(r1['center'][0] - r2['center'][0])
                    horizontal_spacings.append(spacing)
        
        # 计算垂直间距
        vertical_spacings = []
        for i, r1 in enumerate(regions):
            for j, r2 in enumerate(regions):
                if i >= j:
                    continue
                
                # 如果在同一垂直线上
                if abs(r1['center'][0] - r2['center'][0]) <= 20:
                    spacing = abs(r1['center'][1] - r2['center'][1])
                    vertical_spacings.append(spacing)
        
        # 分析间距规律性
        h_pattern = self._analyze_spacing_regularity(horizontal_spacings)
        v_pattern = self._analyze_spacing_regularity(vertical_spacings)
        
        return {
            'horizontal': {
                'spacings': horizontal_spacings,
                'pattern': h_pattern
            },
            'vertical': {
                'spacings': vertical_spacings,
                'pattern': v_pattern
            }
        }
    
    def _analyze_spacing_regularity(self, spacings):
        """分析间距的规律性"""
        if len(spacings) < 2:
            return 'insufficient_data'
        
        # 计算间距的标准差
        mean_spacing = np.mean(spacings)
        std_spacing = np.std(spacings)
        
        # 变异系数（标准差/均值）
        cv = std_spacing / mean_spacing if mean_spacing > 0 else float('inf')
        
        if cv < 0.2:
            return 'regular'  # 规律性间距
        elif cv < 0.5:
            return 'semi_regular'  # 半规律性
        else:
            return 'irregular'  # 不规律
    
    def _identify_layout_mode(self, regions, h_align, v_dist, spacing):
        """识别整体布局模式"""
        num_regions = len(regions)
        
        if num_regions == 1:
            return 'single_text'
        
        # 基于分析结果识别布局模式
        if v_dist['type'] == 'horizontal' and h_align['type'] == 'left':
            return 'horizontal_left_aligned'
        elif v_dist['type'] == 'horizontal' and h_align['type'] == 'center':
            return 'horizontal_centered'
        elif v_dist['type'] == 'vertical' and h_align['type'] == 'left':
            return 'vertical_left_aligned'
        elif v_dist['type'] == 'vertical' and h_align['type'] == 'center':
            return 'vertical_centered'
        elif v_dist['type'] == 'grid_2x2':
            return 'grid_2x2'
        elif num_regions == 2:
            return self._identify_two_text_layout(regions, h_align, v_dist)
        else:
            return 'complex_layout'
    
    def _identify_two_text_layout(self, regions, h_align, v_dist):
        """识别两个文字的布局模式"""
        r1, r2 = regions[0], regions[1]
        
        # 计算相对位置
        dx = abs(r1['center'][0] - r2['center'][0])
        dy = abs(r1['center'][1] - r2['center'][1])
        
        if dx > dy * 2:  # 主要是水平分布
            if r1['center'][0] < r2['center'][0]:
                return 'two_text_left_right'
            else:
                return 'two_text_right_left'
        elif dy > dx * 2:  # 主要是垂直分布
            if r1['center'][1] < r2['center'][1]:
                return 'two_text_top_bottom'
            else:
                return 'two_text_bottom_top'
        else:
            return 'two_text_diagonal'
    
    def _create_simple_layout_result(self, regions):
        """为简单情况创建布局结果"""
        return {
            'regions': regions,
            'horizontal_alignment': {'type': 'single', 'groups': []},
            'vertical_distribution': {'type': 'single', 'columns': 1, 'rows': 1},
            'spacing_pattern': {'type': 'none'},
            'layout_mode': 'single_text' if len(regions) <= 1 else 'simple'
        }
    
    def _print_layout_analysis_result(self, result):
        """打印布局分析结果"""
        print(f"\n布局分析结果:")
        print(f"  文字区域数量: {len(result['regions'])}")
        print(f"  布局模式: {result['layout_mode']}")
        print(f"  水平对齐: {result['horizontal_alignment']['type']}")
        print(f"  垂直分布: {result['vertical_distribution']['type']} "
              f"({result['vertical_distribution']['rows']}行 × {result['vertical_distribution']['columns']}列)")
        
        # 打印对齐组信息
        h_align = result['horizontal_alignment']
        if h_align['type'] != 'single':
            print(f"  对齐组信息:")
            if h_align['left_groups']:
                print(f"    左对齐组: {len(h_align['left_groups'])}个")
            if h_align['right_groups']:
                print(f"    右对齐组: {len(h_align['right_groups'])}个")
            if h_align['center_groups']:
                print(f"    居中对齐组: {len(h_align['center_groups'])}个")
        
        print("=== 布局分析完成 ===\n")
    
    def is_chinese_text(self, text):
        """判断文本是否包含中文字符"""
        import re
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))
    
    def suggest_alignment_strategy(self, layout_result):
        """根据布局分析结果建议对齐策略"""
        layout_mode = layout_result['layout_mode']
        strategies = []
        
        if layout_mode == 'horizontal_left_aligned':
            strategies.append({
                'type': 'preserve_left_alignment',
                'description': '保持左对齐方式',
                'regions': 'all'
            })
        elif layout_mode == 'horizontal_centered':
            strategies.append({
                'type': 'preserve_center_alignment',
                'description': '保持居中对齐方式', 
                'regions': 'all'
            })
        elif layout_mode == 'two_text_left_right':
            strategies.append({
                'type': 'preserve_horizontal_layout',
                'description': '保持左右分布布局',
                'regions': 'all'
            })
        elif layout_mode == 'grid_2x2':
            strategies.append({
                'type': 'preserve_grid_layout',
                'description': '保持2x2网格布局',
                'regions': 'all'
            })
        else:
            strategies.append({
                'type': 'center_alignment',
                'description': '使用居中对齐（默认策略）',
                'regions': 'all'
            })
        
        return strategies 