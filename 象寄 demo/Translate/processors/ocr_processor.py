"""
OCR处理器
负责文字检测与识别
"""
import re
from typing import Optional
from paddleocr import PaddleOCR
from models.data_models import OCRResult, TextRegion, ProcessingResult


class OCRProcessor:
    """OCR处理器"""
    
    def __init__(self):
        """初始化OCR处理器"""
        self._ocr_instance: Optional[PaddleOCR] = None
        self._chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
    
    def _get_ocr_instance(self) -> Optional[PaddleOCR]:
        """获取OCR实例，避免重复初始化"""
        if self._ocr_instance is None:
            print("初始化PaddleOCR...")
            try:
                self._ocr_instance = PaddleOCR(
                    use_doc_orientation_classify=False,
                    use_doc_unwarping=False,
                    use_textline_orientation=False
                )
                print("PaddleOCR初始化完成")
            except Exception as e:
                print(f"PaddleOCR初始化失败: {e}")
                return None
        return self._ocr_instance
    
    def is_chinese_text(self, text: str) -> bool:
        """判断文本是否包含中文字符"""
        return bool(self._chinese_pattern.search(text))
    
    def process_image(self, image_path: str, confidence_threshold: float = 0.5) -> ProcessingResult:
        """
        处理图像进行OCR识别
        
        Args:
            image_path: 图像文件路径
            confidence_threshold: 置信度阈值
            
        Returns:
            ProcessingResult: 包含OCRResult的处理结果
        """
        try:
            # 检查文件是否存在
            import os
            if not os.path.exists(image_path):
                return ProcessingResult.error_result(f"图像文件不存在: {image_path}")

            # 获取OCR实例
            ocr = self._get_ocr_instance()
            if ocr is None:
                return ProcessingResult.error_result("OCR初始化失败")

            print(f"处理图像: {image_path}")

            # OCR处理
            result = ocr.predict(input=image_path)
            if not result:
                return ProcessingResult.error_result("未检测到文字")
            
            # 处理OCR结果
            ocr_result = self._parse_ocr_result(result, confidence_threshold)
            
            print(f"OCR处理完成: 检测到 {ocr_result.total_regions} 个文字区域")
            print(f"  中文区域: {ocr_result.chinese_count} 个")
            print(f"  其他语言区域: {len(ocr_result.other_regions)} 个")
            
            return ProcessingResult.success_result(ocr_result)
            
        except Exception as e:
            error_msg = f"OCR处理失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _parse_ocr_result(self, raw_result, confidence_threshold: float) -> OCRResult:
        """解析原始OCR结果"""
        chinese_regions = []
        other_regions = []
        
        # 提取所有检测结果
        all_dt_polys = []
        all_rec_texts = []
        all_rec_scores = []
        
        for res in raw_result:
            if 'rec_texts' in res and 'dt_polys' in res:
                dt_polys = res['dt_polys']
                rec_texts = res['rec_texts']
                rec_scores = res.get('rec_scores', [1.0] * len(rec_texts))
                
                all_dt_polys.extend(dt_polys)
                all_rec_texts.extend(rec_texts)
                all_rec_scores.extend(rec_scores)
        
        # 创建文字区域对象
        for i, (poly, text, score) in enumerate(zip(all_dt_polys, all_rec_texts, all_rec_scores)):
            # 过滤低置信度结果
            if score < confidence_threshold:
                continue
                
            is_chinese = self.is_chinese_text(text)
            region = TextRegion.from_ocr_result(i, poly, text, score, is_chinese)
            
            if is_chinese:
                chinese_regions.append(region)
                print(f"  中文: '{text}' (置信度: {score:.3f})")
            else:
                other_regions.append(region)
                print(f"  其他: '{text}' (置信度: {score:.3f})")
        
        return OCRResult(
            dt_polys=all_dt_polys,
            rec_texts=all_rec_texts,
            rec_scores=all_rec_scores,
            chinese_regions=chinese_regions,
            other_regions=other_regions
        )
    
    def cleanup(self):
        """清理资源"""
        if self._ocr_instance is not None:
            # PaddleOCR没有显式的清理方法，设置为None让GC处理
            self._ocr_instance = None
            print("OCR实例已清理")
